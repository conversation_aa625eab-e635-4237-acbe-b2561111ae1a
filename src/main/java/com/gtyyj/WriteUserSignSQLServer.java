package com.gtyyj;

import cn.hutool.core.util.StrUtil;
import com.microsoft.sqlserver.jdbc.SQLServerBlob;
import org.apache.commons.io.IOUtils;

import java.io.FileOutputStream;
import java.net.URL;
import java.nio.charset.Charset;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <pre>
 * WriteUserSignSQLServer
 * 把签名数据写到中间库里面（滨海湾）
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/8/7 17:21
 */
public class WriteUserSignSQLServer {

    public static void main(String[] args) throws Exception {

        String signUrl = "https://obs.labway.cn/labway-lims/dongguan/2025/07/30/14/45/8c980b780c4b488b826330330568000b";
        String usercode = "2928";
        String username = "张国聪";

        IOUtils.write(IOUtils.toByteArray(new URL(signUrl).openStream()), new FileOutputStream(StrUtil.format("{}原.jpg", usercode)));

        try {
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            String connectionUrl = "********************************************************************";
            Connection connection = DriverManager.getConnection(connectionUrl, "admin", "admin");

            PreparedStatement insertStatement = connection.prepareStatement("INSERT INTO FWK_USER (USER_ID, USER_NAME, IS_ENABLED, SIGNATURE, SIGNATURE_EN, OPERATE_DATE) VALUES (?, ?, 1, ?, null, sysdatetime());");
            insertStatement.setString(1, usercode);
            insertStatement.setString(2, username);
            insertStatement.setBlob(3, new URL(signUrl).openStream());
            System.out.println(insertStatement.execute());

            PreparedStatement queryStatement = connection.prepareStatement(StrUtil.format("select * from FWK_USER where USER_ID = '{}';", usercode));
            ResultSet resultSet = queryStatement.executeQuery();
            AtomicInteger index = new AtomicInteger(0);
            while (resultSet.next()) {
                IOUtils.write(resultSet.getBytes("SIGNATURE"), new FileOutputStream(index.getAndIncrement() + StrUtil.format("{}.jpg", usercode)));
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}
