import java.io.File;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <pre>
 * DGPisResultSync
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/8/28 14:40
 */
public class DGPisResultSync {

    public static void main(String[] args) throws Exception {
        List<String> lines = Files.readAllLines(new File("D:\\DGPisResultSync.txt").toPath());

        final Set<String> barcodes = new HashSet<>();
        for (String line : lines) {
            String param = "";

            if (line.length() > 12) {
                param = line;
            } else if (line.length() == 12) {
                barcodes.add(line);
            } else if (barcodes.size() == 39) {
                param = String.join(",", barcodes);
            }

            if (!param.isEmpty()) {
                // 执行xxljob请求

                URLConnection urlConnection = new URL("http://10.140.0.129:8080/xxl-job/jobinfo/trigger").openConnection();
                urlConnection.setDoInput(true);

            }
        }

    }

}
