import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <pre>
 * DGPisResultSync
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/8/28 14:40
 */
public class DGPisResultSync {

    public static void main(String[] args) throws Exception {
        List<String> lines = Files.readAllLines(new File("./pis_barcodes.txt").toPath());

        final Set<String> barcodes = new HashSet<>();
        for (String line : lines) {
            String param = "";

            if (line.length() > 12) {
                param = line;
            } else if (line.length() == 12) {
                barcodes.add(line);
            } else if (barcodes.size() == 39) {
                param = String.join(",", barcodes);
            }

            if (!param.isEmpty()) {
                // 执行xxljob请求
                triggerXxlJob(param);
                barcodes.clear(); // 清空已处理的条码
                TimeUnit.SECONDS.sleep(5);
            }
        }

        // 执行xxljob请求
        triggerXxlJob(String.join(",", barcodes));
        barcodes.clear(); // 清空已处理的条码

    }

    /**
     * 触发XXL-JOB任务
     * @param executorParam 执行参数（条码列表）
     */
    private static void triggerXxlJob(String executorParam) {
        System.out.println("开始执行任务：" + executorParam);

        try {
            // 创建URL连接
            URL url = new URL("http://************:5656/xxl-job-admin/jobinfo/trigger");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法
            connection.setRequestMethod("POST");

            // 设置请求头
            connection.setRequestProperty("Accept", "application/json, text/javascript, */*; q=0.01");
            connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            connection.setRequestProperty("X-Requested-With", "XMLHttpRequest");

            // 允许输入输出
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // 构建请求体
            String requestBody = "id=21&executorParam=" + URLEncoder.encode(executorParam, StandardCharsets.UTF_8.toString()) + "&addressList=";

            // 发送请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // 读取响应
            int responseCode = connection.getResponseCode();
            System.out.println("Response Code: " + responseCode);

            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    responseCode >= 200 && responseCode < 300 ? connection.getInputStream() : connection.getErrorStream(),
                    StandardCharsets.UTF_8))) {

                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }

                System.out.println("Response: " + response.toString());
            }

        } catch (Exception e) {
            System.err.println("调用XXL-JOB接口失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

}
